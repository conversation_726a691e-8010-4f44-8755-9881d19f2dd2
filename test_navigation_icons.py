#!/usr/bin/env python3
"""
Test script to verify navigation button icons are working correctly.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PySide6.QtWidgets import QApplication
from src.gui.app import VisionLabAiApp

def test_navigation_icons():
    """Test that navigation buttons have icons."""
    app = QApplication(sys.argv)
    
    # Create the main application window
    window = VisionLabAiApp()
    
    # Check if navigation buttons have icons
    buttons_to_check = [
        ('project_hub_btn', 'Project Hub'),
        ('process_btn', 'Unsupervised Segmentation'),
        ('trainable_btn', 'Trainable Segmentation'),
        ('point_counting_btn', 'Point Counting'),
        ('grain_analysis_btn', 'Grain Analysis'),
        ('advanced_segmentation_btn', 'Advanced Segmentation'),
        ('analysis_btn', 'Image Lab'),
        ('settings_btn', 'Settings'),
        ('ai_assistant_btn', 'AI Assistant')
    ]
    
    print("Testing navigation button icons...")
    
    for btn_attr, btn_name in buttons_to_check:
        if hasattr(window, btn_attr):
            button = getattr(window, btn_attr)
            if button is not None:
                icon = button.icon()
                if not icon.isNull():
                    print(f"✓ {btn_name}: Icon loaded successfully")
                else:
                    print(f"✗ {btn_name}: No icon found")
            else:
                print(f"✗ {btn_name}: Button is None")
        else:
            print(f"✗ {btn_name}: Button attribute not found")
    
    print("\nIcon test completed. You can now check the application visually.")
    
    # Show the window
    window.show()
    
    # Don't start the event loop, just exit
    return True

if __name__ == "__main__":
    test_navigation_icons()
#!/usr/bin/env python3
"""
Test script for theme-aware icons in VisionLab Ai.
This script tests the icon utilities and demonstrates theme-aware icon functionality.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import QSettings
from PySide6.QtGui import QIcon

# Import the icon utilities
from src.gui.utils.icon_utils import (
    create_theme_aware_icon, 
    load_theme_aware_icon, 
    get_tab_icon,
    get_current_theme_info
)

class IconTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Theme-Aware Icons Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Theme info
        is_dark, theme = get_current_theme_info()
        theme_label = QLabel(f"Current Theme: {theme} ({'Dark' if is_dark else 'Light'})")
        layout.addWidget(theme_label)
        
        # Test different icon loading methods
        self.create_icon_test_section(layout)
        
        # Theme toggle button
        toggle_btn = QPushButton("Toggle Theme (Restart Required)")
        toggle_btn.clicked.connect(self.toggle_theme)
        layout.addWidget(toggle_btn)
    
    def create_icon_test_section(self, layout):
        """Create a section to test different icon loading methods."""
        
        # Test tab icons
        tab_layout = QHBoxLayout()
        layout.addLayout(tab_layout)
        
        tab_names = ["Project Hub", "Settings", "AI Assistant", "Point Counting"]
        
        for tab_name in tab_names:
            icon = get_tab_icon(tab_name, size=32, palette=self.palette())
            btn = QPushButton(tab_name)
            btn.setIcon(icon)
            btn.setIconSize(icon.actualSize(icon.availableSizes()[0]) if icon.availableSizes() else (32, 32))
            tab_layout.addWidget(btn)
        
        # Test direct icon loading
        direct_layout = QHBoxLayout()
        layout.addLayout(direct_layout)
        
        icon_files = ["settings.svg", "project_hub.svg", "ai_assistant.svg"]
        
        for icon_file in icon_files:
            icon = load_theme_aware_icon(icon_file, size=32, palette=self.palette())
            btn = QPushButton(f"Direct: {icon_file}")
            btn.setIcon(icon)
            btn.setIconSize((32, 32))
            direct_layout.addWidget(btn)
    
    def toggle_theme(self):
        """Toggle between light and dark theme."""
        try:
            settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
            current_theme = settings.value("app/theme", "Dark Theme")
            
            if "dark" in current_theme.lower():
                new_theme = "Light Theme"
            else:
                new_theme = "Dark Theme"
            
            settings.setValue("app/theme", new_theme)
            print(f"Theme changed to: {new_theme}")
            print("Please restart the application to see the changes.")
            
        except Exception as e:
            print(f"Error toggling theme: {e}")


def main():
    app = QApplication(sys.argv)
    
    # Apply a basic theme for testing
    try:
        settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
        theme = settings.value("app/theme", "Dark Theme").lower()
        
        if "dark" in theme:
            app.setStyleSheet("""
                QMainWindow { background-color: #2b2b2b; color: #ffffff; }
                QPushButton { 
                    background-color: #3c3c3c; 
                    color: #ffffff; 
                    border: 1px solid #555555; 
                    padding: 8px; 
                    border-radius: 4px; 
                }
                QPushButton:hover { background-color: #4c4c4c; }
                QLabel { color: #ffffff; }
            """)
        else:
            app.setStyleSheet("""
                QMainWindow { background-color: #f0f0f0; color: #000000; }
                QPushButton { 
                    background-color: #e0e0e0; 
                    color: #000000; 
                    border: 1px solid #cccccc; 
                    padding: 8px; 
                    border-radius: 4px; 
                }
                QPushButton:hover { background-color: #d0d0d0; }
                QLabel { color: #000000; }
            """)
    except Exception as e:
        print(f"Error applying theme: {e}")
    
    window = IconTestWindow()
    window.show()
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())

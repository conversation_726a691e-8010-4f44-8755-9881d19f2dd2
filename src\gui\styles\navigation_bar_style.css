QToolBar {
    background-color: palette(window);
    spacing: 5px;
    padding: 5px;
    border: none;
    border-bottom: 1px solid palette(mid);
}

QToolButton {
    background-color: transparent;
    color: palette(button-text);
    border: none;
    padding: 10px;
    margin: 0px;
    border-radius: 5px;
}

QToolButton:hover {
    background-color: palette(highlight);
}

QToolButton:checked {
    background-color: palette(link);
    color: palette(highlighted-text);
}
#!/usr/bin/env python3
"""
Test script to verify icon-based navigation implementation
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PySide6.QtGui import QIcon
from PySide6.QtCore import QSize

def test_icons():
    """Test if all navigation icons can be loaded successfully."""
    app = QApplication(sys.argv)
    
    # Icon mapping from the main application
    icon_mapping = {
        "project_hub": "src/gui/icons/project_hub.svg",
        "process": "src/gui/icons/segmentation.svg",
        "trainable": "src/gui/icons/segmentation.svg",
        "point_counting": "src/gui/icons/point_counting.svg",
        "grain_analysis": "src/gui/icons/grain_analysis.svg",
        "advanced_segmentation": "src/gui/icons/segmentation.svg",
        "analysis": "src/gui/icons/image_lab.svg",
        "settings": "src/gui/icons/settings.svg",
        "ai_assistant": "src/gui/icons/ai_assistant.svg"
    }
    
    # Test window
    window = QMainWindow()
    window.setWindowTitle("Icon Navigation Test")
    window.setGeometry(100, 100, 600, 100)
    
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    layout = QHBoxLayout(central_widget)
    
    # Test each icon
    for name, icon_path in icon_mapping.items():
        if os.path.exists(icon_path):
            btn = QPushButton()
            btn.setIcon(QIcon(icon_path))
            btn.setIconSize(QSize(24, 24))
            btn.setToolTip(name.replace('_', ' ').title())
            btn.setMinimumSize(50, 40)
            btn.setMaximumSize(50, 40)
            layout.addWidget(btn)
            print(f"✓ {name}: {icon_path} loaded successfully")
        else:
            print(f"✗ {name}: {icon_path} not found")
    
    window.show()
    print("\nIcon test window displayed. Close it to continue.")
    app.exec()
    print("Icon navigation test completed successfully!")

if __name__ == "__main__":
    test_icons()
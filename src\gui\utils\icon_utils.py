"""
Theme-aware icon utilities for VisionLab Ai application.
This module provides utilities for creating icons that adapt to the current theme.
"""

import os
from PySide6.QtGui import QIcon, QPainter, QPixmap, QPen, QColor
from PySide6.QtCore import QSettings, Qt
from PySide6.QtSvg import QSvg<PERSON><PERSON><PERSON>
from PySide6.QtWidgets import QApplication


def get_current_theme_info():
    """Get information about the current theme."""
    try:
        settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
        theme = settings.value("app/theme", "Dark Theme").lower()
        is_dark = "dark" in theme
        return is_dark, theme
    except Exception:
        return True, "dark"  # Default to dark theme


def get_theme_icon_color(is_dark=None, palette=None):
    """Get the appropriate icon color for the current theme."""
    if is_dark is None:
        is_dark, _ = get_current_theme_info()
    
    if palette is None:
        app = QApplication.instance()
        if app:
            palette = app.palette()
    
    if palette:
        if is_dark:
            # For dark themes, use window text color (usually white/light)
            return palette.color(palette.WindowText)
        else:
            # For light themes, use window text color (usually black/dark)
            return palette.color(palette.WindowText)
    else:
        # Fallback colors
        return QColor(255, 255, 255) if is_dark else QColor(0, 0, 0)


def create_theme_aware_icon(icon_path, size=24, palette=None):
    """
    Creates a theme-aware icon that adapts to the current theme.
    
    Args:
        icon_path (str): Path to the icon file (preferably SVG)
        size (int): Size of the icon in pixels
        palette (QPalette): Optional palette to use for colors
        
    Returns:
        QIcon: Theme-aware icon
    """
    try:
        if not os.path.exists(icon_path):
            return QIcon()
        
        # Get current theme information
        is_dark, theme = get_current_theme_info()
        
        # Get appropriate colors
        if palette is None:
            app = QApplication.instance()
            if app:
                palette = app.palette()
        
        icon_color = get_theme_icon_color(is_dark, palette)
        
        # Create icon
        icon = QIcon()
        
        if icon_path.endswith('.svg'):
            # For SVG files, create theme-aware versions
            renderer = QSvgRenderer(icon_path)
            
            # Create pixmaps for different states
            states = [
                (QIcon.Normal, icon_color),
                (QIcon.Active, icon_color.lighter(120)),
                (QIcon.Selected, palette.color(palette.Highlight) if palette else icon_color.lighter(150)),
                (QIcon.Disabled, icon_color.darker(150))
            ]
            
            for state, color in states:
                pixmap = QPixmap(size, size)
                pixmap.fill(Qt.transparent)
                
                painter = QPainter(pixmap)
                painter.setRenderHint(QPainter.Antialiasing)
                
                # For SVG icons with currentColor, we need to modify the SVG content
                # or use a different approach. For now, we'll render and colorize.
                renderer.render(painter)
                
                # Apply color overlay for theme awareness
                painter.setCompositionMode(QPainter.CompositionMode_SourceIn)
                painter.fillRect(pixmap.rect(), color)
                painter.end()
                
                icon.addPixmap(pixmap, QIcon.Normal if state == QIcon.Normal else state)
        else:
            # For non-SVG files, load directly
            icon = QIcon(icon_path)
        
        return icon
        
    except Exception as e:
        print(f"Error creating theme-aware icon for {icon_path}: {e}")
        # Fallback to regular icon loading
        return QIcon(icon_path) if os.path.exists(icon_path) else QIcon()


def create_colored_icon(icon_path, color, size=24):
    """
    Creates an icon with a specific color.
    
    Args:
        icon_path (str): Path to the icon file
        color (QColor): Color to apply to the icon
        size (int): Size of the icon in pixels
        
    Returns:
        QIcon: Colored icon
    """
    try:
        if not os.path.exists(icon_path):
            return QIcon()
        
        icon = QIcon()
        
        if icon_path.endswith('.svg'):
            renderer = QSvgRenderer(icon_path)
            
            pixmap = QPixmap(size, size)
            pixmap.fill(Qt.transparent)
            
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            
            renderer.render(painter)
            
            # Apply color overlay
            painter.setCompositionMode(QPainter.CompositionMode_SourceIn)
            painter.fillRect(pixmap.rect(), color)
            painter.end()
            
            icon.addPixmap(pixmap)
        else:
            icon = QIcon(icon_path)
        
        return icon
        
    except Exception as e:
        print(f"Error creating colored icon for {icon_path}: {e}")
        return QIcon(icon_path) if os.path.exists(icon_path) else QIcon()


def get_icon_path(icon_name, icons_dir="src/gui/icons"):
    """
    Get the full path to an icon file.
    
    Args:
        icon_name (str): Name of the icon file
        icons_dir (str): Directory containing icons
        
    Returns:
        str: Full path to the icon file
    """
    return os.path.join(icons_dir, icon_name)


def load_theme_aware_icon(icon_name, size=24, icons_dir="src/gui/icons", palette=None):
    """
    Convenience function to load a theme-aware icon by name.
    
    Args:
        icon_name (str): Name of the icon file
        size (int): Size of the icon in pixels
        icons_dir (str): Directory containing icons
        palette (QPalette): Optional palette to use for colors
        
    Returns:
        QIcon: Theme-aware icon
    """
    icon_path = get_icon_path(icon_name, icons_dir)
    return create_theme_aware_icon(icon_path, size, palette)


# Icon mapping for the application
ICON_MAPPING = {
    "Project Hub": "project_hub.svg",
    "Unsupervised Segmentation": "segmentation.svg",
    "Trainable Segmentation": "segmentation.svg",
    "Point Counting": "point_counting.svg",
    "Grain Analysis": "grain_analysis.svg",
    "Advanced Segmentation": "segmentation.svg",
    "Batch Processing": "files.svg",
    "AI Assistant": "ai_assistant.svg",
    "Image Lab": "image_lab.svg",
    "Settings": "settings.svg",
    "About": "about.svg"
}


def get_tab_icon(tab_name, size=24, palette=None):
    """
    Get a theme-aware icon for a specific tab.
    
    Args:
        tab_name (str): Name of the tab
        size (int): Size of the icon in pixels
        palette (QPalette): Optional palette to use for colors
        
    Returns:
        QIcon: Theme-aware icon for the tab
    """
    if tab_name in ICON_MAPPING:
        icon_name = ICON_MAPPING[tab_name]
        return load_theme_aware_icon(icon_name, size, palette=palette)
    else:
        return QIcon()
